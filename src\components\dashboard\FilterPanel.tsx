'use client';

import { useState } from 'react';
import { Search, SlidersHorizontal, ChevronUp, ChevronDown, X, Calendar } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';
import { motion, AnimatePresence } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { ChatFilters } from '@/components/context/ChatDataContext';

interface FilterPanelProps {
  onFilterChange: (filters: ChatFilters) => void;
  currentFilters: ChatFilters;
}

const FilterPanel = ({ onFilterChange, currentFilters }: FilterPanelProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleStatusChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value;
    onFilterChange({
      status: value === 'all' ? undefined : value as 'pending' | 'in_progress' | 'resolved'
    });
  };
  const handlePriorityChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value;
    onFilterChange({
      priority: value === 'all' ? undefined : value as 'low' | 'medium' | 'high' | 'urgent'
    });
  };

  const handleSortChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterChange({
      sort_by: event.target.value as 'latest' | 'priority'
    });
  };
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Searching for:', searchQuery);
  };

  // Check if any filters are applied
  const hasActiveFilters = 
    currentFilters.status !== undefined || 
    currentFilters.priority !== undefined || 
    currentFilters.sort_by !== 'latest';

  const clearAllFilters = () => {
    onFilterChange({
      status: undefined,
      priority: undefined,
      sort_by: 'latest'
    });
  };
    return (
    <Card className="border border-[#e5e7eb] shadow-sm hover:shadow-md overflow-hidden rounded-xl bg-white transition-all duration-300">
      <CardHeader className="pb-5 bg-gradient-to-r from-[#f8f9fa] to-[#ffffff] border-b border-[#e5e7eb] px-6">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg font-semibold flex items-center gap-3 text-[#113158]">
            <SlidersHorizontal className="h-5 w-5 text-[#febd49]" />
            Filtri di Ricerca
          </CardTitle>
          <div className="flex items-center gap-3">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-xs hover:bg-red-50 text-red-600 border border-red-200 rounded-full px-4 py-2 h-auto font-medium transition-all duration-200 hover:border-red-300"
              >
                <X className="h-3.5 w-3.5 mr-1.5" />
                Cancella Tutti
              </Button>
            )}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-xs hover:bg-[#febd49]/10 flex items-center gap-1.5 text-[#113158] rounded-full px-4 py-2 h-auto"
            >
              {isExpanded ? (
                <>
                  <ChevronUp size={16} />
                  <span className="ml-1">Comprimi</span>
                </>
              ) : (
                <>
                  <ChevronDown size={16} />
                  <span className="ml-1">Espandi</span>
                </>
              )}
            </Button>
          </div>
        </div>
        <CardDescription className="text-gray-500 mt-3">
          Filtra e cerca chat di supporto
          {hasActiveFilters && (
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex flex-wrap gap-2 mt-4"
            >
              {currentFilters.status && (
                <Badge variant="secondary" className="bg-[#113158]/10 text-[#113158] px-4 py-1.5 rounded-full text-sm">
                  Stato: {currentFilters.status.replace('_', ' ')}
                </Badge>
              )}
              {currentFilters.priority && (
                <Badge variant="secondary" className="bg-[#febd49]/10 text-[#113158] px-4 py-1.5 rounded-full text-sm">
                  Priorità: {currentFilters.priority}
                </Badge>
              )}
              {currentFilters.sort_by && currentFilters.sort_by !== 'latest' && (
                <Badge variant="secondary" className="bg-[#e0e0e0] text-[#113158] px-4 py-1.5 rounded-full text-sm">
                  Ordina: {currentFilters.sort_by}
                </Badge>
              )}
            </motion.div>
          )}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6 pt-6 px-6">
        {/* Enhanced Search Section */}
        <div className="space-y-3">
          <label className="text-sm font-medium text-[#113158] flex items-center gap-2">
            <Search className="h-4 w-4 text-[#febd49]" />
            Ricerca Rapida
          </label>
          <form onSubmit={handleSearch} className="relative">
            <div className="flex items-center bg-white border-2 border-[#e5e7eb] rounded-lg overflow-hidden focus-within:ring-2 focus-within:ring-[#febd49]/20 focus-within:border-[#febd49] transition-all duration-200">
              <div className="flex items-center justify-center pl-4 pr-2">
                <Search size={18} className="text-gray-400" />
              </div>
              <Input
                placeholder="Cerca per nome utente, email o contenuto messaggio..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 py-3 h-12 text-sm placeholder:text-gray-400"
              />
              {searchQuery && (
                <button
                  type="button"
                  onClick={() => setSearchQuery('')}
                  className="flex items-center justify-center h-full px-3 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X size={18} />
                </button>
              )}
              <Button
                type="submit"
                size="default"
                variant="default"
                className="h-12 bg-[#113158] text-white hover:bg-[#113158]/90 rounded-l-none px-6 font-medium"
              >
                Cerca
              </Button>
            </div>
          </form>
        </div>

        {/* Enhanced Filter Dropdowns */}
        <div className="space-y-4">
          <h3 className="text-sm font-semibold text-[#113158] flex items-center gap-2">
            <SlidersHorizontal className="h-4 w-4 text-[#febd49]" />
            Filtri Avanzati
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label htmlFor="status-filter" className="block text-sm font-medium text-[#113158]">
                Stato Conversazione
              </label>
              <select
                id="status-filter"
                value={currentFilters.status || 'all'}
                onChange={handleStatusChange}
                className="h-11 w-full border-2 border-[#e5e7eb] rounded-lg focus:ring-2 focus:ring-[#febd49]/20 focus:border-[#febd49] transition-all duration-200 bg-white px-3 text-sm"
              >
                <option value="all">🔄 Tutti gli Stati</option>
                <option value="pending">⏳ In Attesa</option>
                <option value="in_progress">🔄 In Elaborazione</option>
                <option value="resolved">✅ Risolte</option>
              </select>
            </div>

            <div className="space-y-2">
              <label htmlFor="priority-filter" className="block text-sm font-medium text-[#113158]">
                Livello di Priorità
              </label>
              <select
                id="priority-filter"
                value={currentFilters.priority || 'all'}
                onChange={handlePriorityChange}
                className="h-11 w-full border-2 border-[#e5e7eb] rounded-lg focus:ring-2 focus:ring-[#febd49]/20 focus:border-[#febd49] transition-all duration-200 bg-white px-3 text-sm"
              >
                <option value="all">📋 Tutte le Priorità</option>
                <option value="low">🟢 Bassa</option>
                <option value="medium">🟡 Media</option>
                <option value="high">🟠 Alta</option>
                <option value="urgent">🔴 Urgente</option>
              </select>
            </div>

            <div className="space-y-2">
              <label htmlFor="sort-by" className="block text-sm font-medium text-[#113158]">
                Ordinamento
              </label>
              <select
                id="sort-by"
                value={currentFilters.sort_by || 'latest'}
                onChange={handleSortChange}
                className="h-11 w-full border-2 border-[#e5e7eb] rounded-lg focus:ring-2 focus:ring-[#febd49]/20 focus:border-[#febd49] transition-all duration-200 bg-white px-3 text-sm"
              >
                <option value="latest">📅 Più Recenti</option>
                <option value="priority">⚡ Per Priorità</option>
                <option value="oldest">📆 Più Vecchie</option>
              </select>
            </div>
          </div>
        </div>
          <AnimatePresence>
          {isExpanded && (
            <motion.div 
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="pt-6 border-t mt-6 border-[#e0e0e0]">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-3">
                    <label htmlFor="date-from" className="block text-sm font-medium text-[#113158] flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-[#febd49]" />
                      Data Da
                    </label>
                    <Input 
                      type="date" 
                      id="date-from" 
                      className="h-12 w-full border-[#e0e0e0] focus-visible:ring-[#febd49] focus-visible:ring-offset-0 focus-visible:border-[#febd49]"
                    />
                  </div>
                  
                  <div className="space-y-3">
                    <label htmlFor="date-to" className="block text-sm font-medium text-[#113158] flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-[#febd49]" />
                      Data A
                    </label>
                    <Input 
                      type="date" 
                      id="date-to" 
                      className="h-12 w-full border-[#e0e0e0] focus-visible:ring-[#febd49] focus-visible:ring-offset-0 focus-visible:border-[#febd49]" 
                    />
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
      
      <CardFooter className="px-8 py-6 bg-[#f9f9f9] border-t border-[#e0e0e0] flex justify-between items-center">
        <div className="text-sm text-gray-500 font-medium">
          {hasActiveFilters 
        ? <span className="flex items-center gap-2"><SlidersHorizontal className="h-4 w-4" /> Filtri applicati</span> 
        : 'Nessun filtro applicato'}
        </div>
        <div className="flex items-center gap-4">
          {hasActiveFilters && (
        <Button 
          variant="ghost"
          size="sm"
          onClick={clearAllFilters}
          className="text-sm text-gray-600 hover:text-[#113158] hover:bg-transparent transition-colors"
        >
          Resetta
        </Button>
          )}
          <Button 
        variant="default" 
        size="lg" 
        className="bg-[#febd49] text-black hover:bg-[#febd49]/90 h-11 px-8 font-medium min-w-[120px]"
        onClick={() => console.log('Applica filtri')}
          >
        Applica Filtri
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default FilterPanel;
