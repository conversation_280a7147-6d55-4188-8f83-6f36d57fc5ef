'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { formatDistanceToNow } from 'date-fns';
import { it } from 'date-fns/locale'; // Import Italian locale properly
import { MessageSquare, AlertCircle, Clock, CheckCircle2 } from 'lucide-react';
import { SupportChat } from './SupportChatList';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

// Enhanced interface with proper typing
interface ChatCardProps {
  chat: SupportChat;
  onClick?: () => void;
  isSelected?: boolean;
}

// Type for priority levels
type PriorityLevel = 'high' | 'medium' | 'low';

// Type for chat status
type ChatStatus = 'pending' | 'in_progress' | 'resolved';

const ChatCard = ({ chat, onClick, isSelected = false }: ChatCardProps) => {
  const router = useRouter();
  const handleCardClick = () => {
    if (onClick) {
      onClick();
    } else {
      // Navigate to individual chat page
      router.push(`/dashboard/chat/${chat.id}`);
    }
  };
  const getPriorityIcon = (priority: PriorityLevel): React.ReactElement | null => {
    switch (priority) {
      case 'high': return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'medium': return <AlertCircle className="h-4 w-4 text-[#febd49]" />;
      case 'low': return <AlertCircle className="h-4 w-4 text-green-500" />;
      default: return null;
    }
  };
  const getStatusBadge = (status: ChatStatus): React.ReactElement => {
    switch (status) {
      case 'pending':
        return <Badge className="bg-gradient-to-r from-[#febd49] to-[#f59e0b] text-white border-0 px-3 py-1.5 font-semibold text-xs shadow-sm">
          <Clock className="mr-1.5 h-3 w-3" /> In Attesa
        </Badge>;
      case 'in_progress':
        return <Badge className="bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0 px-3 py-1.5 font-semibold text-xs shadow-sm">
          <Clock className="mr-1.5 h-3 w-3 animate-spin" /> In Elaborazione
        </Badge>;
      case 'resolved':
        return <Badge className="bg-gradient-to-r from-green-500 to-green-600 text-white border-0 px-3 py-1.5 font-semibold text-xs shadow-sm">
          <CheckCircle2 className="mr-1.5 h-3 w-3" /> Completata
        </Badge>;
      default: return <Badge variant="outline" className="px-3 py-1 text-xs">{status}</Badge>;
    }
  };

  const getPriorityText = (priority: PriorityLevel): string => {
    const priorityMap: { [key: string]: string } = {
      high: 'Priorità Alta',
      medium: 'Priorità Media',
      low: 'Priorità Bassa',
      urgent: 'Urgente'
    };
    return priorityMap[priority] || priority.charAt(0).toUpperCase() + priority.slice(1);
  };

  const formatDate = (dateString: string): string => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: it });
    } catch {
      return 'Data non valida';
    }
  };
    return (
    <Card
      className={`hover:shadow-xl transition-all duration-300 cursor-pointer p-0 h-full rounded-xl overflow-hidden
        ${isSelected ? 'ring-2 ring-[#113158] border-[#113158] shadow-xl' : ''}
        border-[#e5e7eb] hover:border-[#113158]/30 bg-white shadow-sm hover:-translate-y-1
        ${chat.priority === 'urgent' ? 'border-l-4 border-l-red-600' :
          chat.priority === 'high' ? 'border-l-4 border-l-red-500' :
          chat.priority === 'medium' ? 'border-l-4 border-l-[#febd49]' :
          'border-l-4 border-l-green-500'}
      `}
      onClick={handleCardClick}
    >
      <CardHeader className="pb-4 px-6 pt-6">
        <div className="flex justify-between w-full">
          <div className="flex items-center gap-4">
            <Avatar className="h-14 w-14 border-2 border-[#febd49]/30 shadow-lg">
              <AvatarFallback className={`text-white text-lg font-bold ${
                chat.priority === 'urgent' ? 'bg-red-600' :
                chat.priority === 'high' ? 'bg-red-500' :
                chat.priority === 'medium' ? 'bg-[#febd49]' : 'bg-[#113158]'
              }`}>
                {chat.user_name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <CardTitle className="text-base font-bold text-[#113158] mb-1 truncate">
                {chat.user_name}
              </CardTitle>
              <CardDescription className="text-sm text-gray-600 truncate">
                {chat.user_email}
              </CardDescription>
            </div>
          </div>
          <div className="flex flex-col items-end gap-2">
            {getStatusBadge(chat.status as ChatStatus)}
            <span className="text-xs text-gray-500 font-medium">
              {formatDate(chat.updated_at)}
            </span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="px-6 pt-2 pb-5">
        <div className="space-y-3">
          <p className="text-sm line-clamp-2 min-h-[40px] text-gray-700 leading-relaxed">
            {chat.message_snippet}
          </p>

          {/* Priority indicator */}
          <div className="flex items-center gap-2">
            {getPriorityIcon(chat.priority as PriorityLevel)}
            <span className="text-xs font-semibold text-gray-600">
              {getPriorityText(chat.priority as PriorityLevel)}
            </span>
          </div>
        </div>
      </CardContent>

      <CardFooter className="px-6 py-4 border-t border-[#f1f5f9] bg-gradient-to-r from-[#f8f9fa] to-[#ffffff] flex items-center justify-between">
        <div className="flex items-center text-[#113158] hover:text-[#113158]/80 transition-colors">
          <button className="flex items-center px-4 py-2.5 rounded-lg bg-[#113158]/10 hover:bg-[#113158]/15 transition-all duration-200 font-medium">
            <MessageSquare size={16} className="mr-2" />
            <span className="text-sm">Apri Chat</span>
          </button>
        </div>

        <div className="text-xs text-gray-500 font-medium">
          ID: #{chat.id}
        </div>
      </CardFooter>
    </Card>
  );
};

export default ChatCard;
