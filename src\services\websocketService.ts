import { getAccessToken } from './tokenService';

// Define WebSocket connection states
export type WebSocketConnectionState = 'connecting' | 'connected' | 'disconnected' | 'reconnecting' | 'error';

// Base interface for WebSocket messages
export interface WebSocketMessage {
  type: string;
  [key: string]: unknown;
}

// Enhanced message types from the documentation
export interface ChatMessage {
  type: 'chat.message';
  message: string;
  sender: 'support' | 'user';
  timestamp: string;
  id: string;
  chat_id?: number;
  status?: 'sending' | 'sent' | 'delivered' | 'read';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  sender_name?: string;
  attachments?: {
    id: number;
    file_name: string;
    file_url: string;
    content_type: string;
  }[];
}

// Typing indicator message
export interface TypingIndicatorMessage extends WebSocketMessage {
  type: 'typing.indicator' | 'typing.start' | 'typing.stop';
  user_id?: string;
  user_name?: string;
  chat_id?: number;
  is_typing?: boolean;
}

// File upload messages
export interface FileUploadMessage extends WebSocketMessage {
  type: 'file.uploaded' | 'upload.progress' | 'file.upload.notify';
  message_id?: string;
  chat_id?: number;
  progress?: number;
  file_info?: {
    id: number;
    file_name: string;
    file_url: string;
    content_type: string;
  };
}

// Status change message
export interface StatusChangeMessage extends WebSocketMessage {
  type: 'status.change';
  chat_id: number;
  status: 'pending' | 'in_progress' | 'resolved';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  timestamp?: string;
}

// Ping message type
export interface PingMessage {
  type: 'ping';
}

// Pong message type
export interface PongMessage {
  type: 'pong';
}

// Notification message type
export interface NotificationMessage {
  type: string;
  id?: string;
  message?: string;
  timestamp?: string;
  [key: string]: unknown;
}

// Check if code is running on client side
const isClient = typeof window !== 'undefined';

class WebSocketService {
  private socket: WebSocket | null = null;
  private reconnectInterval = 3000; // 3 seconds
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private handlers: Map<string, ((data: WebSocketMessage) => void)[]> = new Map();
  private chatId: string | null = null;
  private pingInterval: NodeJS.Timeout | null = null;
  private connectionStateListeners: ((state: WebSocketConnectionState) => void)[] = [];
  private connectionState: WebSocketConnectionState = 'disconnected';

  constructor() {
    // We initialize on demand when connect is called
  }

  // Get current connection state
  public getConnectionState(): WebSocketConnectionState {
    return this.connectionState;
  }

  // Add listener for connection state changes
  public onConnectionStateChange(listener: (state: WebSocketConnectionState) => void): () => void {
    this.connectionStateListeners.push(listener);
    // Immediately notify the listener of the current state
    listener(this.connectionState);
    
    // Return a function to remove the listener
    return () => {
      this.connectionStateListeners = this.connectionStateListeners.filter(l => l !== listener);
    };
  }

  // Update connection state and notify listeners
  private updateConnectionState(state: WebSocketConnectionState): void {
    this.connectionState = state;
    this.connectionStateListeners.forEach(listener => listener(state));
  }

  /**
   * Connect to the WebSocket support chat endpoint
   * @param chatId ID of the chat to connect to
   */
  public connect(chatId: string): void {
    // Check if window is defined (client-side only)
    if (!isClient) {
      console.warn('WebSocket connect called on server side, skipping');
      return;
    }

    this.chatId = chatId;
    const token = getAccessToken();
    
    if (!token) {
      console.warn('Cannot connect to WebSocket: No authentication token available. Chat will work in polling mode.');
      this.updateConnectionState('disconnected');
      return;
    }
    
    this.updateConnectionState('connecting');
    
    // Check if WebSocket URL environment variable is defined
    const baseWsUrl = process.env.NEXT_PUBLIC_WS_URL;
    if (!baseWsUrl) {
      console.warn('WebSocket URL is not defined. Please set NEXT_PUBLIC_WS_URL environment variable. Chat will work in polling mode.');
      this.updateConnectionState('disconnected');
      return;
    }
    
    // Use the correct WebSocket URL format from the documentation
    const wsUrl = `${baseWsUrl}/ws/support/${chatId}/?token=${token}`;
    
    try {
      this.socket = new WebSocket(wsUrl);
      
      this.socket.onopen = () => {
        console.log('Support chat connection established');
        this.updateConnectionState('connected');
        this.reconnectAttempts = 0;
        
        // Start sending ping messages every 30 seconds to keep the connection alive
        this.startPingInterval();
      };
      
      this.socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          const eventType = data.type;
          
          // Special handling for pong messages
          if (eventType === 'pong') {
            console.log('Received pong from server');
            return;
          }
          
          // Process other message types
          if (this.handlers.has(eventType)) {
            this.handlers.get(eventType)?.forEach(handler => handler(data));
          }
        } catch (error) {
          console.error('Error processing WebSocket message:', error);
        }
      };
      
      this.socket.onclose = (event) => {
        console.log(`Support chat connection closed: ${event.code}`);
        this.stopPingInterval();
        this.updateConnectionState('disconnected');
        
        // Handle specific close codes from the documentation
        switch (event.code) {
          case 4001:
            console.error('WebSocket closed: No authentication token provided');
            break;
          case 4002:
            console.error('WebSocket closed: Invalid authentication token');
            break;
          case 4003:
            console.error('WebSocket closed: Error authenticating token');
            break;
          case 4004:
            console.error('WebSocket closed: No chat ID provided in URL');
            break;
          case 4005:
            console.error('WebSocket closed: Chat ID not found');
            break;
          case 4006:
            console.error('WebSocket closed: Unauthorized access');
            break;
          case 4007:
            console.error('WebSocket closed: Other server error');
            break;
          default:
            // Attempt reconnection for unexpected closes
            this.attemptReconnect();
            break;
        }
      };
        this.socket.onerror = (error) => {
        // Create a more robust error object
        const errorInfo = {
          type: error?.type || 'websocket_error',
          target: (error.target as WebSocket)?.url || wsUrl || 'unknown',
          readyState: this.socket?.readyState ?? 'unknown',
          readyStateText: this.getReadyStateText(this.socket?.readyState),
          timestamp: new Date().toISOString(),
          chatId: this.chatId || 'unknown',
          reconnectAttempts: this.reconnectAttempts,
          errorMessage: this.getErrorMessage(error, this.socket?.readyState)
        };

        // Only log as error if it's an unexpected error, otherwise warn
        if (this.socket?.readyState === WebSocket.CONNECTING) {
          console.warn('Support chat WebSocket connection failed:', errorInfo.errorMessage);
        } else {
          console.error('Support chat WebSocket error:', errorInfo);
        }

        // Only attempt reconnection if we're not already disconnected
        if (this.socket?.readyState !== WebSocket.CLOSED) {
          this.updateConnectionState('disconnected');

          // Attempt reconnection for connection failures
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            console.log(`Will attempt to reconnect in ${this.reconnectInterval}ms...`);
            this.attemptReconnect();
          }
        }
      };
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      this.updateConnectionState('disconnected');
    }
  }

  private startPingInterval(): void {
    // Clear any existing interval
    this.stopPingInterval();
    
    // Send a ping message every 30 seconds as recommended in the docs
    this.pingInterval = setInterval(() => {
      this.sendPing();
    }, 30000); // 30 seconds
  }
  
  private stopPingInterval(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }
  
  private sendPing(): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify({
        type: "ping"
      }));
    }
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnection attempts reached');
      return;
    }
    
    this.reconnectAttempts++;
    this.updateConnectionState('reconnecting');
    
    setTimeout(() => {
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      if (this.chatId) {
        this.connect(this.chatId);
      }
    }, this.reconnectInterval);
  }
  
  public disconnect(): void {
    this.stopPingInterval();
    
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    
    this.chatId = null;
    this.updateConnectionState('disconnected');
  }
  
  /**
   * Send a message over the WebSocket connection
   * @param data The data object to send
   */
  public send(data: WebSocketMessage | PingMessage): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(data));
    } else {
      console.error('Cannot send message: WebSocket is not connected');
    }
  }

  public on(event: string, callback: (data: WebSocketMessage) => void): void {
    if (!this.handlers.has(event)) {
      this.handlers.set(event, []);
    }
    
    this.handlers.get(event)?.push(callback);
  }
  
  public off(event: string, callback: (data: WebSocketMessage) => void): void {
    if (this.handlers.has(event)) {
      const handlers = this.handlers.get(event) || [];
      const index = handlers.indexOf(callback);

      if (index !== -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private getReadyStateText(readyState?: number): string {
    switch (readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING';
      case WebSocket.OPEN: return 'OPEN';
      case WebSocket.CLOSING: return 'CLOSING';
      case WebSocket.CLOSED: return 'CLOSED';
      default: return 'UNKNOWN';
    }
  }

  private getErrorMessage(_error: Event, readyState?: number): string {
    if (readyState === WebSocket.CONNECTING) {
      return 'Failed to establish WebSocket connection. Server may be unavailable.';
    } else if (readyState === WebSocket.OPEN) {
      return 'WebSocket connection lost unexpectedly.';
    } else if (readyState === WebSocket.CLOSING) {
      return 'WebSocket connection is closing.';
    } else if (readyState === WebSocket.CLOSED) {
      return 'WebSocket connection is closed.';
    } else {
      return 'Unknown WebSocket error occurred.';
    }
  }
}

// Singleton instance
export const wsService = new WebSocketService();

// Create a separate notification WebSocket service
class NotificationWebSocketService {
  private socket: WebSocket | null = null;
  private reconnectInterval = 3000; // 3 seconds
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private handlers: Map<string, ((data: NotificationMessage) => void)[]> = new Map();
  private pingInterval: NodeJS.Timeout | null = null;
  private connectionStateListeners: ((state: WebSocketConnectionState) => void)[] = [];
  private connectionState: WebSocketConnectionState = 'disconnected';

  constructor() {
    // We initialize on demand when connect is called
  }

  // Get current connection state
  public getConnectionState(): WebSocketConnectionState {
    return this.connectionState;
  }

  // Add listener for connection state changes
  public onConnectionStateChange(listener: (state: WebSocketConnectionState) => void): () => void {
    this.connectionStateListeners.push(listener);
    // Immediately notify the listener of the current state
    listener(this.connectionState);
    
    // Return a function to remove the listener
    return () => {
      this.connectionStateListeners = this.connectionStateListeners.filter(l => l !== listener);
    };
  }

  // Update connection state and notify listeners
  private updateConnectionState(state: WebSocketConnectionState): void {
    this.connectionState = state;
    this.connectionStateListeners.forEach(listener => listener(state));
  }

  /**
   * Connect to the notifications WebSocket endpoint
   */
  public connect(): void {
    // Check if window is defined (client-side only)
    if (!isClient) {
      console.warn('WebSocket connect called on server side, skipping');
      return;
    }
      this.updateConnectionState('connecting');
    
    // Check if WebSocket URL environment variable is defined
    const baseWsUrl = process.env.NEXT_PUBLIC_WS_URL;
    if (!baseWsUrl) {
      console.error('WebSocket URL is not defined. Please set NEXT_PUBLIC_WS_URL environment variable.');
      this.updateConnectionState('disconnected');
      return;
    }
    
    const wsUrl = `${baseWsUrl}/ws/notifications/`;
    console.log('Connecting to notification WebSocket URL:', wsUrl);
    
    try {
      this.socket = new WebSocket(wsUrl);
      
      this.socket.onopen = () => {
        console.log('Notification WebSocket connection established');
        this.updateConnectionState('connected');
        this.reconnectAttempts = 0;
        
        // Start sending ping messages every 30 seconds to keep the connection alive
        this.startPingInterval();
      };
      
      this.socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          const eventType = data.type;
          
          // Special handling for pong messages
          if (eventType === 'pong') {
            console.log('Received pong from notification server');
            return;
          }
          
          // Process other message types
          if (this.handlers.has(eventType)) {
            this.handlers.get(eventType)?.forEach(handler => handler(data));
          }
        } catch (error) {
          console.error('Error processing Notification WebSocket message:', error);
        }
      };
      
      this.socket.onclose = () => {
        console.log('Notification WebSocket connection closed');
        this.stopPingInterval();
        this.updateConnectionState('disconnected');
        this.attemptReconnect();
      };
        this.socket.onerror = (error) => {
        // Create a more robust error object
        const errorInfo = {
          type: error?.type || 'websocket_error',
          target: (error.target as WebSocket)?.url || wsUrl || 'unknown',
          readyState: this.socket?.readyState ?? 'unknown',
          readyStateText: this.getReadyStateText(this.socket?.readyState),
          timestamp: new Date().toISOString(),
          reconnectAttempts: this.reconnectAttempts,
          errorMessage: this.getErrorMessage(error, this.socket?.readyState)
        };

        // Only log as error if it's an unexpected error, otherwise warn
        if (this.socket?.readyState === WebSocket.CONNECTING) {
          console.warn('Notification WebSocket connection failed:', errorInfo.errorMessage);
        } else {
          console.error('Notification WebSocket error:', errorInfo);
        }

        // Only attempt reconnection if we're not already disconnected
        if (this.socket?.readyState !== WebSocket.CLOSED) {
          this.updateConnectionState('disconnected');

          // Attempt reconnection for connection failures
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            console.log(`Will attempt to reconnect notification WebSocket in ${this.reconnectInterval}ms...`);
            this.attemptReconnect();
          }
        }
      };
    } catch (error) {
      console.error('Error creating Notification WebSocket connection:', error);
      this.updateConnectionState('disconnected');
    }
  }

  private startPingInterval(): void {
    // Clear any existing interval
    this.stopPingInterval();
    
    // Send a ping message every 30 seconds
    this.pingInterval = setInterval(() => {
      this.sendPing();
    }, 30000); // 30 seconds
  }
  
  private stopPingInterval(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }
  
  private sendPing(): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.send({ action: 'ping' });
    }
  }

  private getReadyStateText(readyState?: number): string {
    switch (readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING';
      case WebSocket.OPEN: return 'OPEN';
      case WebSocket.CLOSING: return 'CLOSING';
      case WebSocket.CLOSED: return 'CLOSED';
      default: return 'UNKNOWN';
    }
  }

  private getErrorMessage(_error: Event, readyState?: number): string {
    if (readyState === WebSocket.CONNECTING) {
      return 'Failed to establish notification WebSocket connection. Server may be unavailable.';
    } else if (readyState === WebSocket.OPEN) {
      return 'Notification WebSocket connection lost unexpectedly.';
    } else if (readyState === WebSocket.CLOSING) {
      return 'Notification WebSocket connection is closing.';
    } else if (readyState === WebSocket.CLOSED) {
      return 'Notification WebSocket connection is closed.';
    } else {
      return 'Unknown notification WebSocket error occurred.';
    }
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnection attempts reached for notification WebSocket');
      return;
    }
    
    this.reconnectAttempts++;
    this.updateConnectionState('reconnecting');
    
    setTimeout(() => {
      console.log(`Attempting to reconnect notification WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      this.connect();
    }, this.reconnectInterval);
  }
  
  public disconnect(): void {
    this.stopPingInterval();
    
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    
    this.updateConnectionState('disconnected');
  }
  
  /**
   * Send a message over the WebSocket connection
   * @param data Data to send
   */
  public send(data: NotificationMessage | { action: string; notification_ids?: string[] }): void {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(data));
    } else {
      console.error('Cannot send message: Notification WebSocket is not connected');
    }
  }

  public on(event: string, callback: (data: NotificationMessage) => void): void {
    if (!this.handlers.has(event)) {
      this.handlers.set(event, []);
    }
    
    this.handlers.get(event)?.push(callback);
  }
  
  public off(event: string, callback: (data: NotificationMessage) => void): void {
    if (this.handlers.has(event)) {
      const handlers = this.handlers.get(event) || [];
      const index = handlers.indexOf(callback);
      
      if (index !== -1) {
        handlers.splice(index, 1);
      }
    }
  }
}

// Singleton instance for notifications
export const notificationService = new NotificationWebSocketService();
