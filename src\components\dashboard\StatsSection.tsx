'use client';

import StatsCard from '@/components/dashboard/StatsCard';
import { MessageSquare, Clock, CheckCircle2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface StatsData {
  total: number;
  pending: number;
  inProgress: number;
  resolved: number;
  pendingTrend: number;
  resolvedTrend: number;
  highPriority: number;
}

// In a real application, this data would come from an API
const getStatsData = (): Promise<StatsData> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        total: 42,
        pending: 15,
        inProgress: 8,
        resolved: 19,
        pendingTrend: 5,
        resolvedTrend: 12,
        highPriority: 7,
      });
    }, 500);
  });
};

const StatsSection = () => {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true);
      try {
        const data = await getStatsData();
        setStats(data);
      } catch (error) {
        console.error('Error fetching stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 animate-pulse">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="h-[120px] bg-gray-200 rounded-lg"></div>
        ))}
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  return (
    <div>
      {/* Section Header */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-[#113158] mb-2">
          Panoramica Generale
        </h2>
        <p className="text-sm text-gray-600">
          Statistiche in tempo reale delle conversazioni di supporto
        </p>
      </div>

      {/* Stats Grid */}
      <motion.div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6"
        variants={container}
        initial="hidden"
        animate="show"
      >
        <StatsCard
          title="Conversazioni Totali"
          value={stats.total}
          icon={<MessageSquare size={22} />}
          description="Dall'inizio"
          color="blue"
        />
        <StatsCard
          title="In Attesa"
          value={stats.pending}
          icon={<Clock size={22} />}
          description={`+${stats.pendingTrend} questa settimana`}
          trendValue={stats.pendingTrend}
          trendDirection={stats.pendingTrend > 0 ? 'up' : 'down'}
          color="yellow"
        />
        <StatsCard
          title="In Elaborazione"
          value={stats.inProgress}
          icon={<MessageSquare size={22} />}
          description="Attualmente attive"
          color="blue"
        />
        <StatsCard
          title="Risolte"
          value={stats.resolved}
          icon={<CheckCircle2 size={22} />}
          description={`+${stats.resolvedTrend} questa settimana`}
          trendValue={stats.resolvedTrend}
          trendDirection="up"
          color="green"
        />
      </motion.div>
    </div>
  );
};

export default StatsSection;
