'use client';

import React, { useContext, useEffect, useState } from 'react';
import { AuthContext } from '@/components/context/AuthContext';
import { useChatData, ChatFilters } from '@/components/context/ChatDataContext';
import { AdminRoute } from '@/components/auth/AdminRoute';
import SupportChatList from '@/components/dashboard/SupportChatList';
import ChatDashboardHeader from '@/components/dashboard/ChatDashboardHeader';
import Sidebar from '@/components/dashboard/Sidebar';
import ChatModal from '@/components/dashboard/ChatModal';
import FilterPanel from '@/components/dashboard/FilterPanel';
import ChatDetailView from '@/components/dashboard/ChatDetailView';
import styles from './dashboard.module.css';
import StatsSection from '@/components/dashboard/StatsSection';
import { useRouter, useSearchParams } from 'next/navigation';
import { SupportChat } from '@/components/dashboard/SupportChatList';

export default function DashboardPage() {
  const { isSidepanelOpen } = useContext(AuthContext);  const { 
    selectedChat, 
    setSelectedChat, 
    fetchChatById, 
    updateStatus
  } = useChatData();
  const router = useRouter();
  const searchParams = useSearchParams();  const status = searchParams.get('status');
  const priority = searchParams.get('priority');
  const assignedAgent = searchParams.get('agent');
  const sortBy = searchParams.get('sort');
  const chatId = searchParams.get('chat');

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Initialize based on window size
  useEffect(() => {
    const checkMobile = () => {
      const isMobileView = window.innerWidth < 768;
      setIsMobile(isMobileView);
    };
    
    // Check on component mount
    checkMobile();
    
    // Listen for window resize
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);  
  
  // Check for chat ID in URL  
  useEffect(() => {
    if (chatId) {
      const loadChat = async () => {
        try {
          const chat = await fetchChatById(chatId);
          if (!chat) {
            console.error('Chat not found');
            return;
          }
          setSelectedChat(chat);
          
          // On mobile, we navigate to the chat page
          // On desktop, we show the modal
          if (!isMobile) {
            setIsModalOpen(true);
          } else {
            router.push(`/dashboard/chat/${chatId}`);
          }
        } catch (error) {
          console.error('Failed to load chat:', error);
          // Handle error (maybe show a notification)
        }
      };
      
      loadChat();
    }
  }, [chatId, isMobile, router, fetchChatById, setSelectedChat]);
  
  // Handle chat selection
  const handleChatSelect = async (chat: SupportChat) => {
    // Use the summary from the list but get full details from the API
    try {
      setSelectedChat(chat); // Set immediately for better UX
      
      if (isMobile) {
        // On mobile, navigate to dedicated page
        router.push(`/dashboard/chat/${chat.id}`);
      } else {
        // On desktop, update URL and show modal
        const params = new URLSearchParams(searchParams.toString());
        params.set('chat', chat.id.toString());
        router.push(`/dashboard?${params.toString()}`, { scroll: false });
        setIsModalOpen(true);
        
        // Fetch full details in background
        const fullChat = await fetchChatById(chat.id.toString());
        if (fullChat) {
          setSelectedChat(fullChat);
        }
      }
    } catch (error) {
      console.error('Error handling chat selection:', error);
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsModalOpen(false);
    
    // Remove chat ID from URL
    const params = new URLSearchParams(searchParams);
    params.delete('chat');
    router.push(`/dashboard?${params.toString()}`, { scroll: false });
  };  
  // Handle status update
  const handleStatusUpdate = async (chatId: number, newStatus: 'pending' | 'in_progress' | 'resolved') => {
    if (selectedChat) {
      try {
        // Optimistically update the UI
        setSelectedChat({
          ...selectedChat,
          status: newStatus
        });
        
        // Update via API using ChatDataContext
        const success = await updateStatus(chatId.toString(), newStatus);
        
        if (success) {
          // Broadcast the status change to update any listings
          // Wrapping in a try-catch to prevent errors if the window is closed
          try {
            window.dispatchEvent(new CustomEvent('chat-status-updated', {
              detail: { chatId, status: newStatus }
            }));
            return true; // Explicitly return a value to resolve the promise
          } catch (dispatchError) {
            console.warn('Failed to dispatch event:', dispatchError);
          }
        } else {
          throw new Error('Failed to update status');
        }
      } catch (error) {
        console.error('Error updating chat status:', error);
        if (typeof window !== 'undefined') { // Check if window exists
          alert('Failed to update chat status. Please try again.');
        }
        // Revert the optimistic update 
        try {
          const originalChat = await fetchChatById(chatId.toString());
          if (originalChat) {
            setSelectedChat(originalChat);
          }
        } catch (fetchError) {
          console.warn('Failed to fetch original chat data:', fetchError);
        }
        return false; // Return false on error to properly resolve the promise
      }
    }
    return false; // Return false if no chat is selected to properly resolve the promise
  };
  // Initial filter state based on URL parameters
  const initialFilters: ChatFilters = {
    status: status as 'pending' | 'in_progress' | 'resolved' | undefined,
    priority: priority as 'low' | 'medium' | 'high' | 'urgent' | undefined,
    assigned_agent: assignedAgent || undefined,
    sort_by: (sortBy as 'latest' | 'priority') || 'latest'
  };  
  
  // Handle filter changes with proper type
  const handleFilterChange = (filters: ChatFilters) => {
    const params = new URLSearchParams(searchParams.toString());
    
    // Update the params based on the changed filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value as string);
      } else {
        params.delete(key);
      }
    });
    // Update the URL
    router.push(`/dashboard?${params.toString()}`, { scroll: false });
  };
    return (
    <AdminRoute>
      <div className={`min-h-screen text-black flex flex-col ${styles.dashboard}`}>
        <ChatDashboardHeader />

        {/* Main content area */}
        <div className="flex flex-1">
          {/* Sidebar */}
          {isSidepanelOpen && <Sidebar />}

          {/* Main content with appropriate margin when sidebar is open */}
          <main
            className={`flex-1 transition-all duration-300 ease-in-out ${
              isSidepanelOpen ? 'md:ml-72' : 'ml-0'
            } ${styles.dashboardContent}`}
            style={{ paddingTop: "calc(64px + 1rem)" }}
          >
            <div className={styles.dashboardContainer}>
              {/* Container with consistent vertical spacing */}
              <div className={styles.contentWrapper}>

                {/* Page Title Section */}
                <header>
                  <h1 className={styles.pageTitle}>Portale di Supporto</h1>
                  <p className={styles.pageSubtitle}>
                    Gestisci e monitora tutte le richieste di supporto clienti
                  </p>
                </header>

                {/* Enhanced Stats Section */}
                <section className={styles.statsContainer}>
                  <StatsSection />
                </section>

                {/* Enhanced Filter Panel */}
                <section className={styles.filterSection}>
                  <FilterPanel
                    onFilterChange={handleFilterChange}
                    currentFilters={initialFilters}
                  />
                </section>

                {/* Enhanced Chat List */}
                <section className={styles.chatListContainer}>
                  <header className="mb-6">
                    <h2 className="text-xl font-semibold text-[#113158] mb-2">
                      Conversazioni di Supporto
                    </h2>
                    <p className="text-sm text-gray-600">
                      Visualizza e gestisci tutte le chat di supporto attive
                    </p>
                  </header>
                  <SupportChatList
                    onChatSelect={handleChatSelect}
                    initialFilters={initialFilters}
                  />
                </section>

              </div>
            </div>
          </main>
        </div>

        {/* Chat modal for desktop view */}
        {selectedChat && isModalOpen && (
          <ChatModal
            isOpen={isModalOpen}
            onClose={handleModalClose}
          >
            <ChatDetailView
              chat={selectedChat}
              onBack={handleModalClose}
              onUpdateStatus={(status: 'pending' | 'in_progress' | 'resolved') => handleStatusUpdate(selectedChat.id, status)}
            />
          </ChatModal>
        )}
      </div>
    </AdminRoute>
  );
}