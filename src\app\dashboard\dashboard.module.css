/* Enhanced Dashboard Styles with Improved Spacing and Visual Hierarchy */

.dashboard {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.dashboardContent {
  flex: 1;
  overflow-y: auto;
  width: 100%;
  margin: 0 auto;
  /* Removed excessive padding - now handled by inner container */
}

/* Enhanced container with proper spacing */
.dashboardContainer {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Improved content wrapper with consistent vertical spacing */
.contentWrapper {
  display: flex;
  flex-direction: column;
  gap: 2rem; /* Consistent 32px spacing between sections */
  padding: 1.5rem 0;
}

/* Enhanced stats section container */
.statsContainer {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(17, 49, 88, 0.08);
  border: 1px solid rgba(254, 189, 73, 0.15);
}

/* Improved filter section */
.filterSection {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(17, 49, 88, 0.06);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.filterSection:hover {
  box-shadow: 0 4px 12px rgba(17, 49, 88, 0.1);
}

/* Enhanced chat list container */
.chatListContainer {
  background-color: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(17, 49, 88, 0.06);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.chatListContainer:hover {
  box-shadow: 0 4px 12px rgba(17, 49, 88, 0.08);
}

/* Improved chat grid with better spacing */
.chatGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 1.5rem; /* Increased gap for better breathing room */
  margin-top: 1.5rem;
}

/* Enhanced priority and status styling */
.priority-high {
  color: #dc2626;
  font-weight: 600;
}

.priority-medium {
  color: #febd49;
  font-weight: 600;
}

.priority-low {
  color: #16a34a;
  font-weight: 600;
}

.priority-urgent {
  color: #b91c1c;
  font-weight: 700;
  animation: pulse 2s infinite;
}

.status-pending {
  background: linear-gradient(135deg, #febd49 0%, #f59e0b 100%);
  color: white;
  font-weight: 500;
  border-radius: 6px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-in-progress {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  font-weight: 500;
  border-radius: 6px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-resolved {
  background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
  color: white;
  font-weight: 500;
  border-radius: 6px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Enhanced page title styling */
.pageTitle {
  color: #113158;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  letter-spacing: -0.025em;
}

.pageSubtitle {
  color: #6b7280;
  font-size: 1rem;
  font-weight: 400;
}

/* Responsive design improvements */
@media (max-width: 1024px) {
  .chatGrid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.25rem;
  }

  .dashboardContainer {
    padding: 0 0.75rem;
  }

  .statsContainer,
  .chatListContainer {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .chatGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .dashboardContainer {
    padding: 0 0.5rem;
  }

  .contentWrapper {
    gap: 1.5rem;
    padding: 1rem 0;
  }

  .statsContainer,
  .filterSection,
  .chatListContainer {
    padding: 1rem;
    border-radius: 8px;
  }

  .pageTitle {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .chatGrid {
    gap: 0.75rem;
  }

  .contentWrapper {
    gap: 1rem;
  }

  .pageTitle {
    font-size: 1.5rem;
  }
}

/* Animation for urgent priority */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Enhanced container for consistent layout */
.containerCentered {
  width: 100%;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}
