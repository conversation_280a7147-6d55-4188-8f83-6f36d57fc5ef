"use client"
import { usePathname, useRouter } from 'next/navigation';
import React, { createContext, useState, useEffect, useCallback, useRef } from 'react';
import { deleteToken, verifyToken, login, logout as apiLogout, viewProfile } from '@/services/api';
import { initializeFromStorage, isTokenExpired, getAccessToken, getRefreshToken } from '@/services/tokenService';
import { initializeAuthentication } from '@/services/apiInterceptor';
import { ClientOnly } from '@/components/ClientOnly';

interface User {
    id?: string;
    name?: string;
    email?: string;
    phone?: string;
    is_admin?: boolean;
    is_verified?: boolean;
    is_active?: boolean;
    // Add other user properties as needed
}

interface ApiResponse {
    status: number;
    data?: {
        user?: User;
        error?: string;
    };
}

// Update the LoginResult interface to include status
interface LoginResult {
    success: boolean;
    errorType?: 'unauthorized' | 'invalid_credentials' | 'account_locked' | 'network_error' | 'admin_required';
    message?: string;
    redirectTo?: string;
    status?: number; // Add status code to the interface
}

interface AuthContextType {
    isAuthenticated: boolean;
    isLoading: boolean;
    isSidepanelOpen: boolean;
    user: User | null;
    isAdmin: boolean;
    setIsSidepanelOpen: (boolean: boolean) => void;
    login: (email: string, password: string, isAccessingAdminRoute?: boolean) => Promise<boolean | LoginResult>;
    logout: () => Promise<void>;
    checkAuth: () => Promise<boolean>;
    checkAdminAccess: () => boolean;
}

export const AuthContext = createContext<AuthContextType>({
    isAuthenticated: false,
    isLoading: true,
    isSidepanelOpen: false,
    user: null,
    isAdmin: false,
    setIsSidepanelOpen: () => {},
    login: async () => false,
    logout: async () => {},
    checkAuth: async () => false,
    checkAdminAccess: () => false,
});

interface AuthProviderProps {
    children: React.ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
    const pathname = usePathname();
    const router = useRouter();
    const [isSidepanelOpen, setIsSidepanelOpen] = useState<boolean>(true);
    const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [user, setUser] = useState<User | null>(null);
    const [isAdmin, setIsAdmin] = useState<boolean>(false);
    
    // Refs to prevent duplicate requests
    const isAuthenticatingRef = useRef<boolean>(false);
    const profileFetchedRef = useRef<boolean>(false);
    const initializedRef = useRef<boolean>(false);    // Fetch user profile and update user data
    const fetchUserProfile = useCallback(async (): Promise<User | null> => {
        // Prevent duplicate profile fetches
        if (profileFetchedRef.current) {
            return user;
        }

        try {
            profileFetchedRef.current = true;
            const response = await viewProfile();
            
            if (response.status === 200 && response.data?.user) {
                const userData = response.data.user;
                setUser(userData);
                setIsAdmin(userData.is_admin || false);
                return userData;
            }
            
            // Reset flag on failure so it can be retried
            profileFetchedRef.current = false;
            return null;
        } catch (error) {
            console.error('Error fetching user profile:', error);
            
            // Handle network errors gracefully - don't block authentication
            if (error instanceof Error && error.message.includes('Failed to fetch')) {
                console.warn('Network error while fetching profile. User can still access the app with cached authentication.');
            }
            
            profileFetchedRef.current = false;
            return null;
        }    }, []); // eslint-disable-line react-hooks/exhaustive-deps
    
    // Check if current user has admin access
    const checkAdminAccess = useCallback((): boolean => {
        return isAuthenticated && user?.is_admin === true;
    }, []); // eslint-disable-line react-hooks/exhaustive-deps
    
    // Check if the user is authenticated
    const checkAuthentication = useCallback(async (): Promise<boolean> => {
        // Prevent duplicate authentication checks
        if (isAuthenticatingRef.current) {
            return isAuthenticated;
        }

        isAuthenticatingRef.current = true;
        setIsLoading(true);

        try {
            // First check if we have a valid access token
            const hasAccessToken = !!getAccessToken();
            const hasRefreshToken = !!getRefreshToken();

            // If we have an access token and it's not expired, we're authenticated
            if (hasAccessToken && !isTokenExpired()) {
                setIsAuthenticated(true);
                
                // Only fetch profile if not already fetched
                if (!profileFetchedRef.current) {
                    await fetchUserProfile();
                }
                
                setIsLoading(false);
                isAuthenticatingRef.current = false;
                return true;
            }            // If we have a refresh token, try to get a new access token
            if (hasRefreshToken) {
                try {
                    const initialized = await initializeAuthentication();
                    if (initialized) {
                        setIsAuthenticated(true);
                        
                        // Reset profile fetch flag since we have new tokens
                        profileFetchedRef.current = false;
                        await fetchUserProfile();
                        
                        setIsLoading(false);
                        isAuthenticatingRef.current = false;
                        return true;
                    }
                } catch (error) {
                    console.error('Token refresh failed:', error);
                    
                    // Handle different types of errors
                    if (error instanceof Error) {
                        if (error.message.includes('Network error') || error.message.includes('Network connectivity')) {
                            // Network error - don't clear tokens, let user continue in offline mode if possible
                            console.warn('Network error during authentication. Attempting to verify existing token...');
                            // Continue to server verification below
                        } else if (error.message.includes('session expired') || error.message.includes('log in again')) {
                            // Session expired - clear everything and stop trying
                            setIsAuthenticated(false);
                            setUser(null);
                            setIsAdmin(false);
                            profileFetchedRef.current = false;
                            setIsLoading(false);
                            isAuthenticatingRef.current = false;
                            return false;
                        }
                        // For other errors, continue to server verification
                    }
                }
            }

            // If we have an access token (even if expired), verify with server as last resort
            if (hasAccessToken) {
                const call: ApiResponse = await verifyToken();
                const isValid = call.status === 200;

                if (!isValid) {
                    deleteToken();
                    setIsAuthenticated(false);
                    setUser(null);
                    setIsAdmin(false);
                    profileFetchedRef.current = false;
                } else {
                    setIsAuthenticated(true);
                    
                    // Only fetch profile if not already fetched
                    if (!profileFetchedRef.current) {
                        await fetchUserProfile();
                    }
                }

                setIsLoading(false);
                isAuthenticatingRef.current = false;
                return isValid;
            }

            // No valid tokens
            setIsAuthenticated(false);
            setUser(null);
            setIsAdmin(false);
            profileFetchedRef.current = false;
            setIsLoading(false);
            isAuthenticatingRef.current = false;
            return false;
        } catch (error) {
            console.error('Authentication check failed:', error);
            setIsAuthenticated(false);
            setUser(null);
            setIsAdmin(false);
            profileFetchedRef.current = false;
            setIsLoading(false);
            isAuthenticatingRef.current = false;
            return false;
        }
    }, []); // eslint-disable-line react-hooks/exhaustive-deps
    
    // Initialize authentication on mount
    useEffect(() => {
        // Set initializedRef to false initially
        initializedRef.current = false;
        
        const initializeAuth = async () => {
            if (typeof window !== 'undefined' && !isAuthenticatingRef.current) {
                // Initialize storage first
                initializeFromStorage();
                
                // Run authentication check
                await checkAuthentication();

                // Mark initialization as complete
                initializedRef.current = true;
            }
        };

        initializeAuth();
    }, []); // eslint-disable-line react-hooks/exhaustive-deps
    
    // Handle login
    const handleLogin = async (email: string, password: string, isAccessingAdminRoute?: boolean): Promise<boolean | LoginResult> => {
        setIsLoading(true);
        
        // Reset profile fetch flag for new login
        profileFetchedRef.current = false;
        
        const response: ApiResponse = await login(email, password);

        if (response.status === 200) {
            setIsAuthenticated(true);
            
            // Fetch user profile to get complete user data including admin status
            const userData = await fetchUserProfile();
            
            // If accessing admin route, check if user is admin
            if (isAccessingAdminRoute && (!userData || !userData.is_admin)) {
                // User is authenticated but not admin
                setIsLoading(false);
                return {
                    success: false,
                    errorType: 'admin_required',
                    message: 'Accesso riservato agli amministratori.',
                    redirectTo: '/unauthorized',
                    status: response.status
                };
            }
            
            setIsLoading(false);
            return true;
        }
        
        // Handle unauthorized response (403)
        if (response.status === 403) {
            setIsAuthenticated(false);
            setIsLoading(false);
            
            // Return enhanced result object with more details
            return {
                success: false,
                errorType: 'unauthorized',
                message: response.data?.error || 'Non hai i permessi necessari per accedere a questa area.',
                redirectTo: '/unauthorized',
                status: response.status
            };
        }
        
        // Handle bad request response (400) - invalid credentials
        if (response.status === 400) {
            setIsAuthenticated(false);
            setIsLoading(false);
            
            return {
                success: false,
                errorType: 'invalid_credentials',
                message: response.data?.error || 'Email o password non validi',
                status: response.status
            };
        }

        setIsAuthenticated(false);
        setUser(null);
        setIsAdmin(false);
        setIsLoading(false);
        
        // Return detailed error information for other error cases
        return {
            success: false,
            errorType: 'invalid_credentials',
            message: response.data?.error || 'Email o password non validi',
            status: response.status
        };
    };// Handle logout
    const handleLogout = async (): Promise<void> => {
        setIsLoading(true);
        
        // Reset all flags
        profileFetchedRef.current = false;
        isAuthenticatingRef.current = false;
        
        await apiLogout();
        setIsAuthenticated(false);
        setUser(null);
        setIsAdmin(false);
        setIsLoading(false);
        router.push('/auth');
    };    // Optimized route protection - only check when necessary
    useEffect(() => {
        // Skip if loading or authentication check in progress
        if (isLoading || isAuthenticatingRef.current) return;
        
        // Skip if not fully initialized yet
        if (!initializedRef.current) {
            // Initialize authentication on first render
            if (typeof window !== 'undefined' && !isAuthenticatingRef.current) {
                checkAuthentication();
            }
            return;
        }

        const protectedRoutes = ['/dashboard'];
        const adminRoutes = ['/dashboard'];
        
        // Check if current path requires protection
        const requiresAuth = protectedRoutes.some(route => pathname.startsWith(route));
        const requiresAdmin = adminRoutes.some(route => pathname.startsWith(route));

        if (requiresAuth) {
            // Only redirect if we're definitely not authenticated AND not loading AND initialization is complete
            // This prevents premature redirects during page refresh
            if (!isAuthenticated && !isLoading && initializedRef.current) {
                const redirectPath = encodeURIComponent(pathname);
                router.push(`/auth?redirect=${redirectPath}`);
                return;
            }

            // Check admin access only if authenticated and requires admin and user data is loaded
            if (requiresAdmin && isAuthenticated && !isLoading && user !== null && !user.is_admin) {
                router.push('/unauthorized');
                return;
            }
        }
    }, [pathname, isAuthenticated, isAdmin, isLoading, router, user, checkAuthentication]);

    return (
        <AuthContext.Provider
            value={{
                isAuthenticated,
                isLoading,
                isSidepanelOpen,
                user,
                isAdmin,
                setIsSidepanelOpen,
                login: handleLogin,
                logout: handleLogout,
                checkAuth: checkAuthentication,
                checkAdminAccess
            }}
        >
            <ClientOnly>{children}</ClientOnly>
        </AuthContext.Provider>
    );
};

// Custom hook for using the auth context
export const useAuth = () => {
    const context = React.useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};