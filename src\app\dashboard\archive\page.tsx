'use client';

import React, { useState, useContext } from 'react';
import { AuthContext } from '@/components/context/AuthContext';
import { AdminRoute } from '@/components/auth/AdminRoute';
import ChatDashboardHeader from '@/components/dashboard/ChatDashboardHeader';
import Sidebar from '@/components/dashboard/Sidebar';
import ArchivedChatsView from '@/components/dashboard/ArchivedChatsView';
import { SupportChat } from '@/components/dashboard/SupportChatList';
import ChatModal from '@/components/dashboard/ChatModal';
import ChatDetailView from '@/components/dashboard/ChatDetailView';

// Import the API functions
import { fetchChatById, updateChatStatus } from '@/services/api';

// Function to get a chat by ID using the API
const getChatData = async (id: string): Promise<SupportChat> => {
  try {
    const chatData = await fetchChatById(id);
    if (!chatData) {
      throw new Error('Recupero chat fallito');
    }
    return chatData;
  } catch (error) {
    console.error('Errore nel recupero dei dettagli della chat:', error);
    // Return a fallback object to prevent UI errors
    return {
      id: parseInt(id),
      user_id: (id),
      user_email: "<EMAIL>",
      user_name: "Utente Esempio",
      message_snippet: "Caricamento dettagli fallito. Riprova.",
      priority: "medium",
      status: "resolved",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }
};

export default function ArchivedPage() {
  const { isSidepanelOpen } = useContext(AuthContext);
  const [selectedChat, setSelectedChat] = useState<SupportChat | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Handle chat selection
  const handleChatSelect = async (chat: SupportChat) => {
    try {
      // Use the summary data but try to fetch full details
      setSelectedChat(chat);
      setIsModalOpen(true);
          try {
        const fullChatDetails = await getChatData(chat.id.toString());
        setSelectedChat(fullChatDetails);
      } catch (error) {
        console.error('Recupero dettagli chat completa fallito:', error);
        // Continue with the summary data we already have
      }
    } catch (error) {
      console.error('Errore nella gestione della selezione della chat:', error);
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  // Handle status update
  const handleStatusUpdate = async (chatId: number, newStatus: 'pending' | 'in_progress' | 'resolved') => {
    if (selectedChat) {
      try {
        // Optimistically update the UI
        setSelectedChat({
          ...selectedChat,
          status: newStatus
        });
        
        // Update via API
        const response = await updateChatStatus(chatId, newStatus);
        
        if (response.status !== 200) {
          throw new Error('Failed to update status');
        }
        
        // Force a refresh if moving out of resolved status
        if (newStatus !== 'resolved') {
          // Close the modal if the chat is no longer "resolved"
          handleModalClose();
          
          // Notify the user
          alert('Chat has been moved back to active conversations.');
        }
          // Broadcast status change for other components to hear
        try {
          window.dispatchEvent(new CustomEvent('chat-status-updated', {
            detail: { chatId, status: newStatus }
          }));
        } catch (dispatchError) {
          console.warn('Failed to dispatch status update event:', dispatchError);
        }
      } catch (error) {
        console.error('Error updating chat status:', error);
        alert('Failed to update chat status. Please try again.');
            if (selectedChat) {
          try {
            const refreshedChat = await getChatData(selectedChat.id.toString());
            setSelectedChat(refreshedChat);
          } catch (e) {
            console.error('Failed to refresh chat data', e);
          }
        }
      }
    }
  };
  return (
    <AdminRoute>
      <div className="min-h-screen bg-[var(--background)] text-[var(--text)] flex flex-col">
        <ChatDashboardHeader />
        <div className="flex flex-1 overflow-hidden pt-16">
          {isSidepanelOpen && <Sidebar />}
          <main className={`flex-1 overflow-y-auto transition-all ${isSidepanelOpen ? 'md:ml-72' : ''}`}>
            <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6 max-w-7xl">
              <ArchivedChatsView onChatSelect={handleChatSelect} />
            </div>
          </main>
        </div>        {/* Chat detail modal */}
        {selectedChat && (
          <ChatModal isOpen={isModalOpen} onClose={handleModalClose}>
            <ChatDetailView 
              chat={selectedChat}
              onBack={handleModalClose}
              onUpdateStatus={(status) => handleStatusUpdate(selectedChat.id, status)}
            />
          </ChatModal>
        )}
      </div>
    </AdminRoute>
  );
}
