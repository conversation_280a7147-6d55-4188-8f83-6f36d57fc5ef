'use client';

import { ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { motion } from 'framer-motion';

interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: ReactNode;
  trendValue?: number;
  trendDirection?: 'up' | 'down' | 'neutral';
  color?: 'blue' | 'yellow' | 'green' | 'red';
}

const StatsCard = ({
  title,
  value,
  description,
  icon,
  trendValue,
  trendDirection = 'neutral',
  color = 'blue',
}: StatsCardProps) => {
  const colorClasses = {
    blue: {
      bg: 'bg-gradient-to-br from-[#113158]/5 to-[#113158]/10',
      iconBg: 'bg-gradient-to-br from-[#113158] to-[#0f2a4a]',
      text: 'text-[#113158]',
      border: 'border-[#113158]/15',
      hover: 'hover:shadow-lg hover:shadow-[#113158]/10 hover:border-[#113158]/25',
      iconShadow: 'shadow-lg shadow-[#113158]/25',
      trendUp: 'text-[#113158] bg-[#113158]/10',
      trendDown: 'text-red-600 bg-red-50',
      accent: 'bg-[#113158]/5'
    },
    yellow: {
      bg: 'bg-gradient-to-br from-[#febd49]/5 to-[#febd49]/10',
      iconBg: 'bg-gradient-to-br from-[#febd49] to-[#f59e0b]',
      text: 'text-[#113158]',
      border: 'border-[#febd49]/15',
      hover: 'hover:shadow-lg hover:shadow-[#febd49]/10 hover:border-[#febd49]/25',
      iconShadow: 'shadow-lg shadow-[#febd49]/25',
      trendUp: 'text-green-600 bg-green-50',
      trendDown: 'text-red-600 bg-red-50',
      accent: 'bg-[#febd49]/5'
    },
    green: {
      bg: 'bg-gradient-to-br from-green-50 to-green-100',
      iconBg: 'bg-gradient-to-br from-green-500 to-green-600',
      text: 'text-green-700',
      border: 'border-green-200',
      hover: 'hover:shadow-lg hover:shadow-green-500/10 hover:border-green-300',
      iconShadow: 'shadow-lg shadow-green-500/25',
      trendUp: 'text-green-600 bg-green-50',
      trendDown: 'text-red-600 bg-red-50',
      accent: 'bg-green-50'
    },
    red: {
      bg: 'bg-gradient-to-br from-red-50 to-red-100',
      iconBg: 'bg-gradient-to-br from-red-500 to-red-600',
      text: 'text-red-700',
      border: 'border-red-200',
      hover: 'hover:shadow-lg hover:shadow-red-500/10 hover:border-red-300',
      iconShadow: 'shadow-lg shadow-red-500/25',
      trendUp: 'text-green-600 bg-green-50',
      trendDown: 'text-red-600 bg-red-50',
      accent: 'bg-red-50'
    },
  };
  const trendColorClass = 
    trendDirection === 'up' 
      ? `${colorClasses[color].trendUp} font-medium` 
      : trendDirection === 'down' 
        ? `${colorClasses[color].trendDown} font-medium` 
        : 'text-gray-500';

  const trendIcon = 
    trendDirection === 'up' 
      ? '↑' 
      : trendDirection === 'down' 
        ? '↓' 
        : '→';  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ 
        y: -4, 
        boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)",
        transition: { duration: 0.2 } 
      }}
      className="min-h-[130px]"
    >
      <Card
        className={`border ${colorClasses[color].border} shadow-sm hover:shadow-xl overflow-hidden rounded-xl ${colorClasses[color].bg} ${colorClasses[color].hover} transition-all duration-300 cursor-pointer h-full backdrop-blur-sm`}
      >
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 pt-6 px-6">
          <CardTitle className={`text-sm font-semibold ${colorClasses[color].text} tracking-wide`}>
            {title}
          </CardTitle>
          <motion.div
            className={`rounded-xl p-3 ${colorClasses[color].iconBg} text-white ${colorClasses[color].iconShadow}`}
            whileHover={{ scale: 1.05, rotate: 5 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            {icon}
          </motion.div>
        </CardHeader>
        <CardContent className="px-6 pb-6">
          <motion.div
            className={`text-4xl font-bold ${colorClasses[color].text} mb-3 tracking-tight`}
            initial={{ scale: 1 }}
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            {value}
          </motion.div>

          {/* Enhanced description and trend section */}
          <div className="space-y-2">
            {description && (
              <p className="text-sm text-gray-600 font-medium">
                {description}
              </p>
            )}

            {trendValue !== undefined && (
              <div className="flex items-center gap-2">
                <span className={`flex items-center gap-1 ${trendColorClass} py-1.5 px-3 rounded-full text-xs font-semibold`}>
                  <span className="text-sm leading-none">{trendIcon}</span>
                  {Math.abs(trendValue)}
                </span>
                <span className="text-gray-500 text-xs">
                  {trendDirection === 'up' ? 'in più' : 'in meno'} questa settimana
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default StatsCard;
