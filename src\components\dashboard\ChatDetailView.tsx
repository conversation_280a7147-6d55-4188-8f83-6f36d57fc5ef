﻿'use client';

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { format, formatDistanceToNow } from 'date-fns';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { 
  ArrowLeft, Send, AlertCircle, Clock, CheckCircle, 
  Paperclip as PaperclipIcon, Loader, User,
  X, Download, Search,  FileText, Image as ImageIcon
} from 'lucide-react';
import { createRipple } from '@/lib/utils';
import { SupportChat } from './SupportChatList';
import { useChatData } from '@/components/context/ChatDataContext';
import { wsService, WebSocketConnectionState, WebSocketMessage } from '@/services/websocketService';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { UserDetailSidebar } from './UserDetailSidebar';
import toast from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';
import '@/app/dashboard/chat/chat-styles.css';
import '@/app/dashboard/chat/message-status-animations.css';
import '@/app/dashboard/chat/responsive-message-status.css';
import '@/app/dashboard/chat/ripple-animations.css';
import '@/app/dashboard/chat/attachment-previews.css';
import '@/app/dashboard/chat/emoji-picker.css';
import { MessageStatusIcon } from '@/components/ui/message-status-icon';
import ImageLightbox from '@/components/ui/image-lightbox';
import EmojiPicker from '@/components/ui/emoji-picker';

// Define proper types for WebSocket messages that extend the base type
interface WebSocketStatusChangeMessage extends WebSocketMessage {
  status: 'pending' | 'in_progress' | 'resolved';
  chat_id: number;
  timestamp?: string;
}



interface ChatDetailViewProps {
  chat: SupportChat;
  onBack: () => void;
  onUpdateStatus: (status: 'pending' | 'in_progress' | 'resolved') => void;
}

const ChatDetailView = ({ chat, onBack, onUpdateStatus }: ChatDetailViewProps) => {
  // Use ChatDataContext for messages instead of local state
  const { 
    messages: contextMessages, 
    loadingMessages, 
    fetchMessages, 
    sendMessage: contextSendMessage  } = useChatData();
  
  // Get messages for this specific chat
  const chatId = chat.id.toString();
  const messages = useMemo(() => contextMessages.get(chatId) || [], [contextMessages, chatId]);
  const isLoading = loadingMessages.get(chatId) || false;
  
  const [newMessage, setNewMessage] = useState<string>('');
  const [isSending, setIsSending] = useState<boolean>(false);
  const [isTyping] = useState<boolean>(false);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [, setWsConnectionState] = useState<WebSocketConnectionState>('disconnected');
  const [showUserDetails, setShowUserDetails] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isSearchActive, setIsSearchActive] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<number[]>([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState<number>(-1);
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [isDraggingFile, setIsDraggingFile] = useState<boolean>(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState<boolean>(false);
  const [lightboxImage, setLightboxImage] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const messageInputRef = useRef<HTMLTextAreaElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  
  // Animation variants
  const messageVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { duration: 0.3, ease: "easeOut" }
    }
  };
  
  const typingVariants = {
    animate: {
      scale: [1, 1.2, 1],
      transition: {
        duration: 1,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };
  
  // Handle scroll position and show/hide scroll to bottom button
  useEffect(() => {
    const handleScroll = () => {
      const container = messagesContainerRef.current;
      if (container) {
        const { scrollTop, scrollHeight, clientHeight } = container;
        const isNearBottom = scrollHeight - scrollTop - clientHeight < 200;
        setShowScrollToBottom(!isNearBottom);
      }
    };
    
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);    }
  }, []);
  
  // Typing indicator component
  const TypingIndicator = () => (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="flex items-center gap-3 mb-4 max-w-[75%]"
    >
      <Avatar className='h-8 w-8'>
        <AvatarFallback className='text-xs bg-[#133157] text-white'>
          SA
        </AvatarFallback>
      </Avatar>
      <div className="bg-[#e0e0e0] rounded-lg rounded-bl-md px-4 py-3 flex items-center gap-1">
        <span className="text-xs text-[#133157] mr-2">L&apos;agente sta scrivendo</span>
        <div className="flex gap-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              variants={typingVariants}
              animate="animate"
              style={{ animationDelay: `${i * 0.2}s` }}
              className="w-2 h-2 bg-[#133157]/60 rounded-full"
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
  
  // Drag & drop file handling
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (chat.status !== 'resolved') {
      setIsDraggingFile(true);
    }
  };
  
  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingFile(false);
  };
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (chat.status !== 'resolved') {
      setIsDraggingFile(true);
    }
  };
  
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingFile(false);
    
    if (chat.status === 'resolved' || isSending) {
      return;
    }
    
    // Get dropped files
    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      // Filter for supported file types
      const validFileTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'application/msword', 
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel', 
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain', 'text/csv'
      ];
      
      const supportedFiles = droppedFiles.filter(file => 
        validFileTypes.includes(file.type) || 
        /\.(jpg|jpeg|png|gif|webp|pdf|doc|docx|xls|xlsx|ppt|pptx|txt|csv)$/i.test(file.name)
      );
      
      if (supportedFiles.length > 0) {
        setAttachments(prev => [...prev, ...supportedFiles]);
        
        // Focus text input after dropping files
        messageInputRef.current?.focus();
        
        if (supportedFiles.length !== droppedFiles.length) {
          toast.error(`${droppedFiles.length - supportedFiles.length} file non supportati sono stati ignorati.`);
        }
      } else {
        toast.error('Nessun tipo di file supportato trovato. Si prega di utilizzare immagini, documenti o PDF.');
      }
    }
  };  // Fetch messages when component mounts
  useEffect(() => {
    if (chat && chat.id) {
      fetchMessages(chatId);
    }
  }, [chat, chatId, fetchMessages]);
    
  // Use WebSockets for real-time updates if available
  useEffect(() => {
    const wsEnabled = process.env.NEXT_PUBLIC_WS_ENABLED === 'true';
    
    if (wsEnabled && chat && chat.id) {
      // Connect to WebSocket for this chat
      wsService.connect(chat.id.toString());
      
      // Listen to connection state changes
      const unsubscribe = wsService.onConnectionStateChange((state) => {
        setWsConnectionState(state);
      });
        
      // Set up event handler for new messages with type assertion
      const handleNewMessage = (data: WebSocketMessage) => {
        // Type guard to check if this is a message event
        if ('message' in data && data.type === 'chat.message') {
          // Invalidate messages cache and refetch to get updated data
          fetchMessages(chatId, true);
          toast.success('Nuovo messaggio ricevuto');
        }
      };
      
      // Set up event handler for status changes with type assertion
      const handleStatusChange = (data: WebSocketMessage) => {
        // Type guard to check if this is a status change event
        if ('status' in data && 'chat_id' in data && data.chat_id === chat.id) {
          const statusData = data as WebSocketStatusChangeMessage;
          onUpdateStatus(statusData.status);
          toast.success(`Stato aggiornato a: ${
            statusData.status === 'pending' ? 'In attesa' :
            statusData.status === 'in_progress' ? 'In corso' :
            statusData.status === 'resolved' ? 'Risolto' : statusData.status
          }`);
        }
      };
      
      // Register WebSocket event handlers
      wsService.on('new_message', handleNewMessage);
      wsService.on('status_change', handleStatusChange);
      
      return () => {
        // Clean up WebSocket connection and event handlers
        wsService.off('new_message', handleNewMessage);
        wsService.off('status_change', handleStatusChange);
        wsService.disconnect();
        unsubscribe(); // Clean up connection state listener
      };
    } else {
      // Fallback to polling if WebSockets are disabled
      toast('WebSockets disabilitati, utilizzo del polling', {
        icon: 'ℹ️',
      });
      const intervalId = setInterval(() => fetchMessages(chatId), 15000);
      return () => clearInterval(intervalId);
    }
  }, [chat, chatId, fetchMessages, onUpdateStatus]);
    // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  const handleSendMessage = async () => {
    if (!newMessage.trim() && attachments.length === 0) return;
    
    setIsSending(true);
    
    // Create loading toast
    const loadingToastId = toast.loading('Invio messaggio...');
    
    try {
      // Use context sendMessage method which handles optimistic updates
      const success = await contextSendMessage(chatId, newMessage, attachments);
      
      if (success) {
        setNewMessage('');
        setAttachments([]);
        toast.success('Messaggio inviato con successo!', { id: loadingToastId });
        
        // Scroll to bottom to show new message
        setTimeout(() => scrollToBottom(), 100);
      } else {
        toast.error('Errore durante l\'invio del messaggio', { id: loadingToastId });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Errore durante l\'invio del messaggio', { id: loadingToastId });
    } finally {
      setIsSending(false);
    }
  };
  
  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffInHours = Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60);
      
      if (diffInHours < 24) {
        return formatDistanceToNow(date, { addSuffix: true });
      } else {
        return format(date, 'MMM d, h:mm a');
      }
    } catch {
      return 'Unknown time';
    }
  };
  
  // Search functionality
  const handleSearch = () => {
    if (!searchQuery.trim()) {
      setIsSearchActive(false);
      setSearchResults([]);
      setCurrentSearchIndex(-1);
      return;
    }
    
    const results: number[] = [];
    
    // Find all messages that match the search query
    messages.forEach((message, index) => {
      if (message.content.toLowerCase().includes(searchQuery.toLowerCase())) {
        results.push(index);
      }
    });
    
    setSearchResults(results);
    setIsSearchActive(true);
    setCurrentSearchIndex(results.length > 0 ? 0 : -1);
    
    // Scroll to first result if found
    if (results.length > 0) {
      scrollToMessage(results[0]);
    }
  };
  
  const scrollToMessage = (messageIndex: number) => {
    const messageElement = document.getElementById(`message-${messageIndex}`);
    if (messageElement) {
      messageElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
      
      // Add a highlight effect
      messageElement.classList.add('search-highlight');
      setTimeout(() => {
        messageElement.classList.remove('search-highlight');
      }, 1500);
    }
  };
  
  const handleNextSearchResult = () => {
    if (searchResults.length === 0) return;
    
    const nextIndex = (currentSearchIndex + 1) % searchResults.length;
    setCurrentSearchIndex(nextIndex);
    scrollToMessage(searchResults[nextIndex]);
  };
  
  const handlePreviousSearchResult = () => {
    if (searchResults.length === 0) return;
    
    const prevIndex = (currentSearchIndex - 1 + searchResults.length) % searchResults.length;
    setCurrentSearchIndex(prevIndex);
    scrollToMessage(searchResults[prevIndex]);
  };
  
  const handleClearSearch = () => {
    setSearchQuery('');
    setIsSearchActive(false);
    setSearchResults([]);
    setCurrentSearchIndex(-1);
  };
  
  // Get the priority icon
  const getPriorityIcon = () => {
    switch (chat.priority) {
      case 'high': 
        return <AlertCircle size={14} className='text-destructive' />;
      case 'medium': 
        return <AlertCircle size={14} className='text-amber-500' />;
      case 'low': 
        return <AlertCircle size={14} className='text-emerald-500' />;
      default: 
        return null;
    }
  };
  
  // Get status badge
  const getStatusBadge = () => {
    switch (chat.status) {
      case 'pending': 
        return (
          <Badge variant='outline' className='bg-amber-100 text-amber-800 dark:bg-amber-900/70 dark:text-amber-300 border-amber-200 dark:border-amber-800/50 ml-2 px-2.5 py-1'>
            <Clock className='mr-1.5 h-3.5 w-3.5' /> In attesa
          </Badge>
        );
      case 'in_progress': 
        return (
          <Badge variant='outline' className='bg-blue-100 text-blue-800 dark:bg-blue-900/70 dark:text-blue-300 border-blue-200 dark:border-blue-800/50 ml-2 px-2.5 py-1'>
            <Clock className='mr-1.5 h-3.5 w-3.5 animate-spin' /> In corso
          </Badge>
        );
      case 'resolved': 
        return (
          <Badge variant='outline' className='bg-emerald-100 text-emerald-800 dark:bg-emerald-900/70 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800/50 ml-2 px-2.5 py-1'>
            <CheckCircle className='mr-1.5 h-3.5 w-3.5' /> Risolto
          </Badge>
        );
      default: 
        return null;
    }
  };
  
  // Add mobile detection and touch handling
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  
  // Touch handling for mobile
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    const distance = touchStart - touchEnd;
    const isRightSwipe = distance < -50;

    if (isRightSwipe && isMobile) {
      // Swipe right to go back on mobile
      onBack();
    }
  };
  
  // Handle emoji selection
  const handleEmojiSelect = (emoji: string) => {
    if (messageInputRef.current) {
      const start = messageInputRef.current.selectionStart || newMessage.length;
      const end = messageInputRef.current.selectionEnd || newMessage.length;
      
      // Insert emoji at cursor position
      const newValue = newMessage.substring(0, start) + emoji + newMessage.substring(end);
      setNewMessage(newValue);
      
      // Set cursor position after the emoji
      setTimeout(() => {
        if (messageInputRef.current) {
          const newPosition = start + emoji.length;
          messageInputRef.current.selectionStart = newPosition;
          messageInputRef.current.selectionEnd = newPosition;
          messageInputRef.current.focus();
        }
      }, 10);
    } else {
      // If no cursor position (e.g., on mobile), append to the end
      setNewMessage(prevMessage => prevMessage + emoji);
    }
  };

  return (
    <>
      <div 
        className='flex flex-col h-full bg-white'
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        {/* User detail sidebar */}
        <UserDetailSidebar 
          chat={chat}
          isOpen={showUserDetails}
          onClose={() => setShowUserDetails(false)}
          onUpdateStatus={onUpdateStatus}
        />
        
        {/* Overlay for sidebar */}
        {showUserDetails && (
          <div 
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
            onClick={() => setShowUserDetails(false)}
          />
        )}
        
        {/* Chat header - properly closed */}
        <div className='bg-white border-b border-[#e0e0e0] p-3 md:p-4 sticky top-16 z-10 shadow-sm'>
          {/* Back button and user info */}
          <div className='flex items-center gap-3 mb-3 md:mb-0'>
            <Button 
              onClick={onBack}
              size='icon'
              variant='ghost'
              className='rounded-full hover:bg-[#133157]/5 text-[#133157] h-9 w-9 flex-shrink-0'
            >
              <ArrowLeft size={18} />
            </Button>
            <div className='min-w-0 flex-1'>
              <div className='flex items-center gap-2 flex-wrap'>
                <h2 className='font-semibold text-lg text-[#133157] truncate'>{chat.user_name}</h2>
                {getStatusBadge()}
              </div>
              <div className='flex items-center text-xs text-[#133157]/60 gap-1.5 mt-0.5 flex-wrap'>
                <span className='truncate max-w-[180px]'>{chat.user_email}</span>
                <span className='hidden sm:inline mx-1'>•</span>
                <span className='flex items-center gap-1'>
                  {getPriorityIcon()}
                  <span className='capitalize'>
                    {chat.priority === 'high' ? 'Alta priorità' : 
                     chat.priority === 'medium' ? 'Media priorità' : 
                     chat.priority === 'low' ? 'Bassa priorità' : `${chat.priority} priorità`}
                  </span>
                </span>
              </div>
            </div>
          </div>
          
          {/* Action buttons row */}
          <div className='flex items-center gap-2.5 md:gap-3 mt-2 md:mt-0 overflow-x-auto pb-1 md:pb-0 scrollbar-hide'>
            {/* Search component with expanding functionality */}
            <AnimatePresence>
              {isSearchActive ? (
                <motion.div
                  initial={{ width: 40, opacity: 0.5 }}
                  animate={{ width: "100%", opacity: 1 }}
                  exit={{ width: 40, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="relative flex items-center gap-1 bg-white rounded-lg border border-[#e0e0e0] shadow-sm h-9 flex-1 max-w-md"
                >
                  <Search size={14} className="ml-2.5 text-[#133157]/60" />
                  <Input 
                    type="text"
                    placeholder="Cerca messaggi..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleSearch();
                      }
                    }}
                    className="h-full border-none shadow-none focus-visible:ring-0 bg-transparent text-sm px-1"
                    autoFocus
                  />
                  <div className="flex items-center gap-1 mr-1.5">
                    {searchResults.length > 0 && (
                      <span className="text-xs text-[#133157]/60 font-medium hidden sm:inline">
                        {currentSearchIndex + 1} / {searchResults.length}
                      </span>
                    )}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 hover:bg-[#133157]/5"
                      onClick={handlePreviousSearchResult}
                      disabled={searchResults.length === 0}
                    >
                      <ArrowLeft size={14} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 hover:bg-[#133157]/5"
                      onClick={handleNextSearchResult}
                      disabled={searchResults.length === 0}
                    >
                      <ArrowLeft size={14} className="rotate-180" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 hover:bg-[#133157]/5"
                      onClick={handleClearSearch}
                    >
                      <X size={14} />
                    </Button>
                  </div>
                </motion.div>
              ) : (
                <Button
                  variant="outline"
                  size="icon"
                  className="h-9 w-9 border-[#e0e0e0] hover:bg-[#133157]/5 hover:border-[#133157]/20 flex-shrink-0 rounded-full"
                  onClick={() => setIsSearchActive(true)}
                >
                  <Search size={15} />
                </Button>
              )}
            </AnimatePresence>
            
            {/* Details button - Show on tablets and larger, collapse to icon on mobile */}
            <Button
              onClick={() => setShowUserDetails(true)}
              variant="outline"
              size="icon"
              className="h-9 w-9 hover:bg-[#133157]/5 border-[#e0e0e0] text-[#133157] hover:border-[#133157]/20 flex-shrink-0 rounded-full"
              title="Visualizza dettagli utente"
            >
              <User className="h-4 w-4" />
            </Button>
            
            {/* Spacer to push status buttons to the right */}
            <div className="flex-1"></div>
            
            {/* Status toggle buttons with consistent sizing and better spacing */}
            <div className="flex items-center gap-3 md:gap-4">
              <Button
                onClick={() => {
                  onUpdateStatus('pending');
                  toast.success('Stato aggiornato a: In attesa');
                }}
                variant={chat.status === 'pending' ? 'default' : 'outline'}
                size="icon"
                className={`h-9 w-9 flex-shrink-0 rounded-full ${chat.status === 'pending' 
                  ? 'bg-[#febd49] hover:bg-[#febd49]/90 text-[#133157] border-[#febd49]' 
                  : 'hover:bg-[#febd49]/10 hover:text-[#133157] border-[#e0e0e0] hover:border-[#febd49]/30'
                }`}
                title="Segna come In attesa"
              >
                <Clock className="h-4 w-4" />
              </Button>
              
              <Button
                onClick={() => {
                  onUpdateStatus('in_progress');
                  toast.success('Stato aggiornato a: In corso');
                }}
                variant={chat.status === 'in_progress' ? 'default' : 'outline'}
                size="icon"
                className={`h-9 w-9 flex-shrink-0 rounded-full ${chat.status === 'in_progress' 
                  ? 'bg-[#133157] hover:bg-[#133157]/90 text-white border-[#133157]' 
                  : 'hover:bg-[#133157]/10 hover:text-[#133157] border-[#e0e0e0] hover:border-[#133157]/30'
                }`}
                title="Segna come In corso"
              >
                <Clock className={`h-4 w-4 ${chat.status === 'in_progress' ? 'animate-spin' : ''}`} />
              </Button>
              
              <Button
                onClick={() => {
                  onUpdateStatus('resolved');
                  toast.success('Stato aggiornato a: Risolto');
                }}
                variant={chat.status === 'resolved' ? 'default' : 'outline'}
                size="icon"
                className={`h-9 w-9 flex-shrink-0 rounded-full ${chat.status === 'resolved'
                  ? 'bg-green-600 hover:bg-green-700 text-white border-green-600' 
                  : 'hover:bg-green-50 hover:text-green-700 border-[#e0e0e0] hover:border-green-200'
                }`}
                title="Segna come Risolto"
              >
                <CheckCircle className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Message container - Properly positioned with adequate top spacing */}
        <div className='flex-1 overflow-hidden flex flex-col'>
          {/* Chat messages */}
          <div className='flex-1 overflow-y-auto px-4 md:px-6 pt-8 pb-6 space-y-4 relative' ref={messagesContainerRef}>
            {/* File drop overlay */}
            {isDraggingFile && (
              <div className="absolute inset-0 bg-[#133157]/10 backdrop-blur-sm flex items-center justify-center z-10 rounded-lg border-2 border-dashed border-[#133157]">
                <div className="bg-white p-6 rounded-xl shadow-lg text-center">
                  <PaperclipIcon className="w-12 h-12 text-[#133157] mx-auto mb-4" />
                  
                  <p className="text-[#133157]/60 mt-1">Immagini, documenti e PDF fino a 10MB</p>
                </div>
              </div>
            )}
            
            {/* Scroll to bottom button */}
            <AnimatePresence>
              {showScrollToBottom && (
                <motion.button
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  className="scroll-to-bottom-button"
                  onClick={scrollToBottom}
                  aria-label="Scorri in fondo"
                >
                  <ArrowLeft className="w-4 h-4 rotate-90" />
                </motion.button>
              )}
            </AnimatePresence>
            
            {isLoading ? (
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className='flex justify-center items-center h-full'
              >
                <div className='flex flex-col items-center'>
                  <div className='w-12 h-12 border-4 border-[#133157]/20 border-t-[#133157] rounded-full animate-spin mb-4'></div>
                  <p className='text-[#133157]/70 font-medium'>Caricamento messaggi...</p>
                </div>
              </motion.div>
            ) : messages.length === 0 ? (
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className='flex justify-center items-center h-full'
              >
                <div className='text-center'>
                  <p className='text-[#133157]/70 text-lg font-medium'>Nessun messaggio ancora</p>
                  <p className='text-[#133157]/50 text-sm mt-2'>Inizia la conversazione inviando un messaggio</p>
                </div>
              </motion.div>
            ) : (
              <AnimatePresence>
                {isTyping && <TypingIndicator />}
                {messages.map((message, index) => {
                  const isAgent = message.sender_type === 'agent';
                  const isConsecutive = index > 0 && messages[index - 1].sender_type === message.sender_type;
                  
                  return (
                    <motion.div
                      key={message.id}
                      id={`message-${index}`}
                      variants={messageVariants}
                      initial="hidden"
                      animate="visible"
                      className={`message-container ${isAgent ? 'message-container-agent' : 'message-container-user'} ${
                        searchResults.includes(index) ? 'search-result' : ''
                      } ${currentSearchIndex === searchResults.indexOf(index) && isSearchActive ? 'current-search-result' : ''}`}
                    >
                      <div className={`flex gap-3 group hardware-accelerated ${isAgent ? 'flex-row-reverse' : 'flex-row'}`}>
                        {/* Avatar */}
                        <div className={`flex-shrink-0 ${isConsecutive ? 'opacity-0' : 'opacity-100'}`}>
                          <Avatar className='h-8 w-8'>
                            <AvatarFallback 
                              className={`text-xs font-medium ${isAgent ? 'avatar-agent' : 'avatar-user'}`}
                            >
                              {isAgent ? 'SA' : message.sender_name.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                        </div>

                        {/* Message Content */}
                        <div className={`flex flex-col ${isAgent ? 'items-end' : 'items-start'} w-full`}>
                          {/* Sender name and timestamp */}
                          {!isConsecutive && (
                            <div className={`flex items-center gap-2 mb-1 ${isAgent ? 'flex-row-reverse' : 'flex-row'}`}>
                              <span className={`text-xs font-medium ${
                                isAgent ? 'text-[#133157]' : 'text-[#133157]/70'
                              }`}>
                                {isAgent ? 'Support Agent' : message.sender_name}
                              </span>
                              <span className='text-xs text-[#133157]/40'>
                                {formatTimestamp(message.timestamp)}
                              </span>
                            </div>
                          )}
                          
                          {/* Message bubble */}
                          <div className={
                              isAgent
                              ? `relative px-4 py-3 max-w-full message-bubble-agent text-white ${!isConsecutive ? 'message-bubble-agent-tail' : ''}` 
                              : `relative px-4 py-3 max-w-full message-bubble-user text-[#133157] ${!isConsecutive ? 'message-bubble-user-tail' : ''}`
                            }
                            role={isAgent ? 'region' : 'region'}
                            aria-label={isAgent ? 'Support agent message' : 'User message'}
                          >
                            {/* Message text */}
                            <p className='text-sm leading-relaxed whitespace-pre-wrap break-words'>
                              {message.content}
                            </p>
                            
                            {/* Message status (always shown for user messages) */}
                            {!isAgent && message.status && (
                              <div className="flex items-center gap-2 ml-1 mt-1">
                                <MessageStatusIcon
                                  status={message.status}
                                  showTooltip={true}
                                />
                              </div>
                            )}

                            {/* Attachments */}
                            {message.attachments && message.attachments.length > 0 && (
                              <div className='mt-3 pt-3 border-t border-current/10'>
                                <div className='flex flex-wrap gap-2'>
                                  {message.attachments.map(attachment => {
                                    const isImage = /\.(jpg|jpeg|png|gif|webp)$/i.test(attachment.file_name);
                                    const isPdf = /\.pdf$/i.test(attachment.file_name);
                                    const isDocument = /\.(doc|docx|xls|xlsx|ppt|pptx|txt|csv)$/i.test(attachment.file_name);
                                      
                                    return (
                                      <div
                                        key={attachment.id}
                                        className="attachment-container"
                                      >
                                        {isImage ? (
                                          <div 
                                            className="attachment-preview-image"
                                            onClick={() => setLightboxImage(attachment.file_url)}
                                          >
                                            <Image
                                              src={attachment.file_url}
                                              alt={attachment.file_name}
                                              className="rounded-lg shadow-sm"
                                              width={200}
                                              height={150}
                                              style={{ objectFit: 'contain' }}
                                              priority={false}
                                            />
                                          </div>
                                        ) : (
                                          <motion.div 
                                            whileHover={{ scale: 1.02 }}
                                            className={`attachment-card flex items-center gap-2 text-xs ${
                                              isAgent ? 'text-white/90' : 'text-[#133157]/90'
                                            }`}
                                          >
                                            {isPdf ? (
                                              <FileText className="w-4 h-4 file-icon" />
                                            ) : isDocument ? (
                                              <FileText className="w-4 h-4 file-icon" />
                                            ) : (
                                              <PaperclipIcon className="w-4 h-4 file-icon" />
                                            )}
                                            
                                            <a 
                                              href={attachment.file_url}
                                              target='_blank'
                                              rel='noopener noreferrer'
                                              className='hover:underline max-w-[120px] truncate font-medium'
                                              title={attachment.file_name}
                                            >
                                              {attachment.file_name}
                                            </a>
                                            
                                            <Download className="w-3 h-3 opacity-70" />
                                          </motion.div>
                                        )}
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            )}
                          </div>
                          
                          {/* Message status and timestamp for consecutive messages */}
                          <div className={`flex items-center gap-2 mt-1 ${isAgent ? 'flex-row-reverse' : 'flex-row'}`}>
                            {isAgent && message.status && (
                              <motion.div 
                                initial={{ opacity: 0 }}
                                whileHover={{ opacity: 1 }}
                                className="opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <MessageStatusIcon status={message.status} showTooltip={false} />
                              </motion.div>
                            )}
                            {isConsecutive && (
                              <span className='text-xs text-[#133157]/40 opacity-0 group-hover:opacity-100 transition-opacity'>
                                {formatTimestamp(message.timestamp)}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            )}
            <div ref={messagesEndRef} />
          </div>
          
          {/* Message input */}
          <div className='border-t border-[#e0e0e0] bg-white p-4 md:p-6'>
            {/* Attachment previews */}
            <AnimatePresence>
              {attachments.length > 0 && (
                <motion.div 
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className='mb-4'
                >
                  <div className='flex justify-between items-center mb-3'>
                    <h4 className='text-sm font-semibold text-[#133157]'>
                      File allegati ({attachments.length})
                    </h4>
                    {attachments.length > 1 && (
                      <button 
                        onClick={() => setAttachments([])}
                        className='text-xs text-red-500 hover:text-red-600 transition-colors font-medium'
                      >
                        Rimuovi tutti
                      </button>
                    )}
                  </div>
                  
                  <div className='attachment-preview-grid'>
                    {attachments.map((file, index) => {
                      const isImage = /\.(jpg|jpeg|png|gif|webp)$/i.test(file.name);
                      const isPdf = /\.pdf$/i.test(file.name);
                      const isDocument = /\.(doc|docx|xls|xlsx|ppt|pptx|txt|csv)$/i.test(file.name);
                      const fileSize = file.size < 1024 * 1024 
                        ? `${Math.round(file.size / 1024)} KB` 
                        : `${(file.size / (1024 * 1024)).toFixed(1)} MB`;
                      
                      return (
                        <motion.div 
                          key={index}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.9 }}
                          className='attachment-preview group'
                        >
                          <div className='h-10 w-10 rounded-lg bg-[#133157] flex items-center justify-center shrink-0'>
                            {isPdf ? (
                              <FileText className="w-5 h-5 text-white" />
                            ) : isImage ? (
                              <ImageIcon className="w-5 h-5 text-white" />
                            ) : isDocument ? (
                              <FileText className="w-5 h-5 text-white" />
                            ) : (
                              <PaperclipIcon className="w-5 h-5 text-white" />
                            )}
                          </div>
                          <div className='flex-1 min-w-0'>
                            <p className='font-medium text-[#133157] text-sm truncate' title={file.name}>
                              {file.name}
                            </p>
                            <p className='text-[#133157]/60 text-xs mt-0.5'>{fileSize}</p>
                          </div>
                          <button 
                            onClick={() => setAttachments(prev => prev.filter((_, i) => i !== index))}
                            className='attachment-preview-close'
                            aria-label="Rimuovi allegato"
                            title="Rimuovi allegato"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </motion.div>
                      );
                    })}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
            
            {/* Input area */}
            <div className='flex items-center space-x-2'>
              <div className='flex-1 relative bg-[#f5f5f5] rounded-full border border-[#e0e0e0] h-12'>
                <div className='relative h-full'>
                  <textarea
                    ref={messageInputRef}
                    value={newMessage}
                    onChange={(e) => {
                      setNewMessage(e.target.value);
                      // Auto-resize textarea but keep within bounds
                      const textarea = e.target;
                      textarea.style.height = 'auto';
                      const newHeight = Math.min(textarea.scrollHeight, 120);
                      textarea.style.height = newHeight + 'px';

                      // Adjust container height for multi-line
                      const container = textarea.closest('.relative')?.parentElement as HTMLElement;
                      if (container) {
                        if (newHeight > 48) {
                          container.style.height = (newHeight + 8) + 'px';
                          container.classList.remove('rounded-full');
                          container.classList.add('rounded-xl');
                        } else {
                          container.style.height = '48px';
                          container.classList.remove('rounded-xl');
                          container.classList.add('rounded-full');
                        }
                      }
                    }}
                    className='w-full resize-none py-3 px-4 pr-20 bg-transparent focus:outline-none text-[#133157] h-full'
                    placeholder='Scrivi il tuo messaggio...'
                    disabled={chat.status === 'resolved' || isSending}
                    rows={1}
                    style={{ height: '48px', minHeight: '48px' }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                    aria-label="Message input"
                  />

                  <div className='absolute right-0 top-0 h-full flex items-center pr-3'>
                    {/* Emoji picker */}
                    <div className='relative mr-1'>
                      <EmojiPicker onEmojiSelect={handleEmojiSelect} />
                    </div>

                    {/* Attach button */}
                    <button
                      onClick={(e) => {
                        createRipple(e);
                        fileInputRef.current?.click();
                      }}
                      className='w-8 h-8 flex items-center justify-center text-[#133157]/60 hover:text-[#133157] hover:bg-[#133157]/5 rounded-full transition-all'
                      disabled={chat.status === 'resolved' || isSending}
                      title='Allega file'
                      aria-label="Attach files"
                    >
                      <PaperclipIcon className="w-4 h-4" />
                      <input
                        type='file'
                        ref={fileInputRef}
                        className='hidden'
                        multiple
                        accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv"
                        onChange={(e) => {
                          if (e.target.files) {
                            const newFiles = Array.from(e.target.files);
                            setAttachments(prev => [...prev, ...newFiles]);
                            e.target.value = '';
                          }
                        }}
                      />
                    </button>
                  </div>
                </div>
              </div>

              {/* Character count - moved outside and made smaller */}
              {newMessage.length > 1800 && (
                <div className={`text-xs absolute -top-6 right-20 ${
                  newMessage.length > 1900 ? 'text-amber-500' : 'text-[#133157]/40'
                } ${newMessage.length >= 2000 ? 'text-red-500 font-medium' : ''}`}>
                  <span>{newMessage.length}/2000</span>
                </div>
              )}
              
              {/* Send button */}
              <motion.button
                onClick={(e) => {
                  createRipple(e);
                  handleSendMessage();
                }}
                disabled={((!newMessage.trim() && attachments.length === 0) || chat.status === 'resolved' || isSending)}
                className={`flex items-center justify-center gap-2 px-4 py-3 rounded-lg bg-[#133157] text-white font-medium h-12 min-w-[80px] ${
                  ((!newMessage.trim() && attachments.length === 0) || chat.status === 'resolved' || isSending)
                  ? 'opacity-50 cursor-not-allowed' : 'hover:bg-[#133157]/90'
                }`}
                aria-label="Send message"
                whileTap={{ scale: 0.95 }}
                whileHover={{ scale: 1.05 }}
              >
                {isSending ? (
                  <>
                    <Loader className="w-4 h-4 animate-spin" />
                    <span className="hidden sm:inline">Sending...</span>
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4" />
                    <span className="hidden sm:inline">Invia</span>
                  </>
                )}
              </motion.button>
            </div>
            
            {/* Status warning */}
            <AnimatePresence>
              {chat.status === 'resolved' && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className='mt-4 p-3 bg-amber-50 border border-amber-200 rounded-xl'
                >
                  <p className='text-sm text-amber-700 font-medium'>
                    ⚠️ Questa chat è stata risolta. Cambia lo stato per continuare la conversazione.
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
      
      {/* Image Lightbox */}
      <AnimatePresence>
        {lightboxImage && (
          <ImageLightbox 
            imageSrc={lightboxImage} 
            onClose={() => setLightboxImage(null)} 
          />
        )}
      </AnimatePresence>
    </>
  );
};

export default ChatDetailView;