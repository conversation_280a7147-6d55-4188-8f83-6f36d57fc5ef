'use client';

import React, { useContext} from 'react';
import { AuthContext } from '@/components/context/AuthContext';
import { MenuIcon, HeadphonesIcon } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

const ChatDashboardHeader = () => {
  const { isSidepanelOpen, setIsSidepanelOpen, user } = useContext(AuthContext);
  
  return (
    <header className="h-16 bg-white dark:bg-gray-900 border-b-2 border-[#e5e7eb] dark:border-gray-800 fixed top-0 left-0 right-0 z-30 flex items-center justify-between px-4 md:px-6 shadow-sm">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          className="rounded-lg hover:bg-[#febd49]/10 dark:hover:bg-gray-800 transition-all duration-200"
          onClick={() => setIsSidepanelOpen(!isSidepanelOpen)}
          aria-label={isSidepanelOpen ? "Chiudi menu laterale" : "Apri menu laterale"}
        >
          <MenuIcon className="h-5 w-5 text-[#113158]" />
        </Button>

        <Link href="/dashboard" className="flex items-center gap-3 hover:opacity-80 transition-opacity">
          <div className="p-2 rounded-lg bg-gradient-to-br from-[#febd49] to-[#f59e0b] shadow-md">
            <HeadphonesIcon className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-[#113158] dark:text-white">Centro Assistenza</h1>
            <p className="text-xs text-gray-600 dark:text-gray-300 hidden sm:block">Gestione Supporto Clienti</p>
          </div>
        </Link>
      </div>

      <div className="flex items-center gap-3">
        {user && (
          <div className="flex items-center gap-3">
            <div className="text-right hidden md:block">
              <p className="text-sm font-medium text-[#113158] dark:text-white">
                {user.name || 'Operatore'}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-300">
                {user.email}
              </p>
            </div>
            <Avatar className="h-10 w-10 border-2 border-[#febd49]/30 shadow-md">
              <AvatarFallback className="bg-gradient-to-br from-[#113158] to-[#0f2a4a] text-white text-sm font-bold">
                {user.name ? user.name[0].toUpperCase() : user.email ? user.email[0].toUpperCase() : 'U'}
              </AvatarFallback>
            </Avatar>
          </div>
        )}
      </div>
    </header>
  );
};

export default ChatDashboardHeader;